/*
 * Copyright (C) 2008-2014 TrinityCore <http://www.trinitycore.org/>
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

#ifndef TRINITY_CHALLENGE_H
#define TRINITY_CHALLENGE_H

#include "Common.h"
#include "Scenario.h"
#include "InstanceScript.h"

struct ChallengeEntry;
class Scenario;

enum ChallengeSpells : uint32
{
    ChallengersMight                = 206150, /// generic creature aura
    ChallengersBurden               = 206151, /// generic player aura
    ChallengerBolstering            = 209859,
    ChallengerNecrotic              = 209858,
    ChallengerOverflowing           = 221772,
    ChallengerSanguine              = 226489,
    ChallengerRaging                = 228318,
    ChallengerSummonVolcanicPlume   = 209861,
    ChallengerVolcanicPlume         = 209862,
    ChallengerBursting              = 240443,
    ChallengerQuake                 = 240447,
    ChallengerGrievousWound         = 240559,

    //Explosive
    SPELL_FEL_EXPLOSIVES_SUMMON_1   = 240444, //Short dist
    SPELL_FEL_EXPLOSIVES_SUMMON_2   = 243110, //Long dist
    SPELL_FEL_EXPLOSIVES_VISUAL     = 240445,
    SPELL_FEL_EXPLOSIVES_DMG        = 240446,

    SPELL_CHALLENGE_ANTIKICK        = 305284,
};

enum ChallengeNpcs : uint32
{
    NpcVolcanicPlume        = 105877,
    NPC_FEL_EXPLOSIVES      = 120651,
};

enum MiscChallengeData : uint32
{
    ChallengeDelayTimer     = 10,

};

class Challenge : public InstanceScript
{
public:
    Challenge(Map* map, Player* player, uint32 instanceID, Scenario* scenario);
    ~Challenge();

    void OnPlayerEnterForScript(Player* player) override;	// 当玩家进入副本时调用的脚本函数
    void OnPlayerLeaveForScript(Player* player) override;	// 当玩家离开副本时调用的脚本函数
    void OnPlayerDiesForScript(Player* player) override;	// 当玩家死亡时调用的脚本函数
    void OnCreatureCreateForScript(Creature* creature) override;	// 当副本内的生物被创建时调用的脚本函数
    void OnCreatureRemoveForScript(Creature* creature) override;	// 当副本内的生物被移除时调用的脚本函数
    void OnCreatureUpdateDifficulty(Creature* creature) override;	// 当副本内的生物难度更新时调用的脚本函数
    void EnterCombatForScript(Creature* creature, Unit* enemy) override;	// 当生物进入战斗状态时调用的脚本函数
    void CreatureDiesForScript(Creature* creature, Unit* killer) override;	// 当生物死亡时调用的脚本函数
    void OnGameObjectCreateForScript(GameObject* /*go*/) override {}		// 当游戏对象被创建时调用的脚本函数（此函数为空）
    void OnGameObjectRemoveForScript(GameObject* /*go*/) override {}		// 当游戏对象被移除时调用的脚本函数（此函数为空）
    void OnUnitCharmed(Unit* unit, Unit* charmer) override;			// 当单位被魅惑状态控制时调用的脚本函数
    void OnUnitRemoveCharmed(Unit* unit, Unit* charmer) override;	// 当单位的魅惑状态被移除时调用的脚本函数

    void Update(uint32 diff) override;			// 更新副本逻辑的函数，每次Tick都会调用

    bool CanStart();							// 判断是否可以开始副本
    void Start();								// 开始副本
    void Complete();							// 完成副本

    void BroadcastPacket(WorldPacket const* data) const override;			// 广播数据包到副本内的所有玩家

    void HitTimer();							// 计时器的处理逻辑 暂停计时器刷新箱子奖励

    uint32 GetChallengeLevel() const;			// 获取挑战模式的等级
    std::array<uint32, 3> GetAffixes() const;	// 获取挑战模式的词缀
    bool HasAffix(Affixes affix);				// 判断副本是否具有指定的词缀

    uint32 GetChallengeTimerToNow() const;		// 获取当前距离挑战模式开始的时间
    void ModChallengeTimer(uint32 timer);		// 修改挑战模式的计时器
    uint32 GetChallengeTimer();					// 获取挑战模式的计时器

    void ResetGo();								// 重置游戏对象（GameObject）的状态
    void SendStartTimer(Player* player = nullptr);			// 发送副本开始计时器给指定玩家（或所有玩家）
    void SendStartElapsedTimer(Player* player = nullptr);	// 发送副本已经经过的时间给指定玩家（或所有玩家）
    void SendChallengeModeStart(Player* player = nullptr);	// 发送挑战模式开始的消息给指定玩家（或所有玩家）
    void SendChallengeModeNewPlayerRecord(Player* player);	// 发送挑战模式新的玩家记录给指定玩家
    void SendChallengeModeMapStatsUpdate(Player* player);	// 发送挑战模式地图统计信息更新给指定玩家
    void SummonWall(Player* player);						// 召唤墙壁（特定功能的游戏对象）
    uint8 GetItemCount(ObjectGuid guid) const;				// 获取特定对象（ObjectGuid）的物品数量
    uint8 GetLevelBonus() const;							// 获取等级加成

    void SetInstanceScript(InstanceScript* instanceScript);	// 设置副本脚本（InstanceScript）
    InstanceScript* GetInstanceScript() const;				// 获取副本脚本（InstanceScript）

    GuidUnorderedSet _challengers;
    bool _checkStart;
    bool _canRun;
    bool _run;
    bool _complete;

    ObjectGuid m_gguid;
    ObjectGuid m_ownerGuid;
    ObjectGuid m_itemGuid;

    uint32 _challengeTimer;
    uint32 _affixQuakingTimer;

    FunctionProcessor m_Functions;
    uint32 _mapID;

private:
    std::map<ObjectGuid, uint8> _countItems;

    ObjectGuid _creator;
    std::array<uint32, 3> _affixes;
    std::bitset<size_t(Affixes::MaxAffixes)> _affixesTest;
    uint16 _chestTimers[3];
    Item* _item;
    Map* _map;
    InstanceScript* _instanceScript;
    MapChallengeModeEntry const* _challengeEntry;
    uint32 _challengeLevel;
    uint32 _instanceID;
    uint8 _rewardLevel;
    bool _isKeyDepleted;
    Scenario* _scenario;
    uint32 _deathCount = 0;
};

#endif
